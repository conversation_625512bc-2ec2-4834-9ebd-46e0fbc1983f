<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:version='2' class='v2' expr:dir='data:blog.languageDirection' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
  <meta charset='utf-8'/>
  <meta content='width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1' name='viewport'/>
  <title><data:blog.pageTitle/></title>
  <b:include data='blog' name='all-head-content'/>
  
  <!-- SEO Meta Tags -->
  <meta expr:content='data:blog.metaDescription' name='description'/>
  <meta content='sports, streaming, football, soccer, live matches, statistics' name='keywords'/>
  <meta content='index, follow' name='robots'/>
  <meta content='H-A-2025' name='author'/>
  <meta content='website' property='og:type'/>
  <meta expr:content='data:blog.pageTitle' property='og:title'/>
  <meta expr:content='data:blog.metaDescription' property='og:description'/>
  <meta expr:content='data:blog.url' property='og:url'/>
  <meta content='Sports Streaming Platform' property='og:site_name'/>
  <link href='https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&amp;display=swap' rel='stylesheet'/>
  <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css' rel='stylesheet'/>
  
  <!-- Preload Critical Resources -->
  <link href='https://fonts.gstatic.com' rel='preconnect'/>
  <link href='https://cdnjs.cloudflare.com' rel='preconnect'/>
  <link href='https://www.youtube.com' rel='preconnect'/>
  
  <!-- Favicon -->
  <link href='https://i.imgur.com/favicon.ico' rel='icon' type='image/x-icon'/>
  
  <!-- CSS Styles -->
  <b:skin><![CDATA[
/* Variables */
:root {
  --dark-primary: #000080;
  --light-primary: #0000FF;
  --dark-text: #ffffff;
  --light-text: #333333;
  --accent-color: #FF0000;
  --background-dark: #121212;
  --background-light: #f5f5f5;
  --card-dark: #1e1e1e;
  --card-light: #ffffff;
  --shadow-dark: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --border-radius: 8px;
  --font-family: 'Roboto', sans-serif;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  transition: var(--transition);
  overflow-x: hidden;
}

body.dark-mode {
  background-color: var(--background-dark);
  color: var(--dark-text);
}

body.light-mode {
  background-color: var(--background-light);
  color: var(--light-text);
}

a {
  text-decoration: none;
  transition: var(--transition);
}

img {
  max-width: 100%;
  height: auto;
}

/* Lazy Loading for Images */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s;
}

.lazy-load.loaded {
  opacity: 1;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Header */
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 15px 0;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.dark-mode .header {
  background-color: var(--dark-primary);
  box-shadow: var(--shadow-dark);
}

.light-mode .header {
  background-color: var(--light-primary);
  box-shadow: var(--shadow-light);
}

.header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
}

.logo img {
  height: 40px;
}

/* Navigation */
.nav-menu ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu {
  display: block;
}

.nav-item {
  margin-left: 20px;
}

.nav-link {
  color: #fff;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.nav-link:hover, .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}

/* Settings Bar */
.settings-bar {
  display: flex;
  align-items: center;
}

.theme-toggle, .lang-selector {
  background: none;
  border: none;
  color: #fff;
  margin-left: 15px;
  cursor: pointer;
  font-size: 18px;
}

.lang-selector select {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  padding: 5px;
  border-radius: var(--border-radius);
}

/* News Ticker */
.news-ticker {
  background-color: var(--accent-color);
  color: #fff;
  padding: 10px 0;
  overflow: hidden;
  position: relative;
}

.news-ticker-label {
  display: inline-block;
  padding: 0 15px;
  font-weight: 700;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.news-ticker-content {
  display: inline-block;
  white-space: nowrap;
  animation: ticker 30s linear infinite;
}

@keyframes ticker {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/* Main Content */
.main-content {
  padding: 30px 0;
}

/* Tab Content */
.tab-content {
  display: none;
  padding: 20px;
  background-color: var(--card-light);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  margin-top: 20px;
  transition: var(--transition);
}

.dark-mode .tab-content {
  background-color: var(--card-dark);
  box-shadow: var(--shadow-dark);
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Match Calendar */
.match-calendar {
  margin-bottom: 30px;
}

.calendar-nav {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.calendar-day {
  flex: 1;
  text-align: center;
  padding: 10px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.calendar-day.yesterday {
  background-color: #f0f0f0;
}

.calendar-day.today {
  background-color: #e6f7ff;
  font-weight: 700;
}

.calendar-day.tomorrow {
  background-color: #f9f9f9;
}

.dark-mode .calendar-day.yesterday {
  background-color: #2a2a2a;
}

.dark-mode .calendar-day.today {
  background-color: #003366;
}

.dark-mode .calendar-day.tomorrow {
  background-color: #333333;
}

.match-list {
  list-style: none;
}

.match-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: var(--border-radius);
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
}

.dark-mode .match-item {
  background-color: #2a2a2a;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.match-teams {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.team {
  display: flex;
  align-items: center;
  flex: 1;
}

.team-logo {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.team-name {
  font-weight: 500;
}

.match-info {
  text-align: center;
  padding: 0 15px;
}

.match-time {
  font-weight: 700;
  margin-bottom: 5px;
}

.match-status {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
}

.match-status.not-started {
  background-color: #f0f0f0;
  color: #666;
}

.match-status.soon {
  background-color: #fff8e1;
  color: #ff9800;
}

.match-status.live {
  background-color: #ff5252;
  color: #fff;
  animation: blink 1s infinite;
}

.match-status.ended {
  background-color: #e0e0e0;
  color: #333;
}

/* League Statistics */
.league-stats {
  margin-bottom: 30px;
}

.stats-selector {
  margin-bottom: 15px;
}

.stats-selector select {
  width: 100%;
  padding: 10px;
  border-radius: var(--border-radius);
  border: 1px solid #ddd;
  background-color: #fff;
}

.dark-mode .stats-selector select {
  background-color: #2a2a2a;
  border-color: #444;
  color: #fff;
}

.standings-table {
  width: 100%;
  border-collapse: collapse;
}

.standings-table th, .standings-table td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.dark-mode .standings-table th, .dark-mode .standings-table td {
  border-color: #444;
}

.standings-table th {
  background-color: #f5f5f5;
  font-weight: 500;
}

.dark-mode .standings-table th {
  background-color: #333;
}

.standings-table tr:hover {
  background-color: #f9f9f9;
}

.dark-mode .standings-table tr:hover {
  background-color: #3a3a3a;
}

/* Video Player */
.video-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  margin-bottom: 20px;
}

.video-container iframe, .video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius);
}

.player-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.player-selector {
  padding: 8px 12px;
  border-radius: var(--border-radius);
  border: 1px solid #ddd;
  background-color: #fff;
}

.dark-mode .player-selector {
  background-color: #2a2a2a;
  border-color: #444;
  color: #fff;
}

/* TV Channels */
.channels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.channel-card {
  background-color: #fff;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.dark-mode .channel-card {
  background-color: #2a2a2a;
  box-shadow: var(--shadow-dark);
}

.channel-card:hover {
  transform: translateY(-5px);
}

.channel-logo {
  width: 100%;
  height: 120px;
  object-fit: contain;
  padding: 15px;
  background-color: #f5f5f5;
}

.dark-mode .channel-logo {
  background-color: #333;
}

.channel-info {
  padding: 15px;
}

.channel-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.channel-description {
  font-size: 14px;
  color: #666;
}

.dark-mode .channel-description {
  color: #aaa;
}

/* News Section */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.news-card {
  background-color: #fff;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.dark-mode .news-card {
  background-color: #2a2a2a;
  box-shadow: var(--shadow-dark);
}

.news-card:hover {
  transform: translateY(-5px);
}

.news-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.news-content {
  padding: 15px;
}

.news-date {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.dark-mode .news-date {
  color: #aaa;
}

.news-title {
  font-weight: 500;
  margin-bottom: 10px;
}

.news-excerpt {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.dark-mode .news-excerpt {
  color: #aaa;
}

.read-more {
  display: inline-block;
  padding: 5px 10px;
  background-color: var(--light-primary);
  color: #fff;
  border-radius: var(--border-radius);
  font-size: 12px;
  transition: var(--transition);
}

.read-more:hover {
  background-color: var(--dark-primary);
}

/* Footer */
.footer {
  background-color: #222;
  color: #fff;
  padding: 40px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.footer-section h3 {
  font-size: 18px;
  margin-bottom: 15px;
  position: relative;
  padding-bottom: 10px;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 2px;
  background-color: var(--accent-color);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #aaa;
  transition: var(--transition);
}

.footer-links a:hover {
  color: #fff;
  padding-left: 5px;
}

.social-links {
  display: flex;
  margin-top: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin-right: 10px;
  color: #fff;
  transition: var(--transition);
}

.social-links a:hover {
  background-color: var(--accent-color);
  transform: translateY(-3px);
}

.copyright {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  color: #aaa;
}

/* Ad Spaces */
.ad-space {
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: var(--border-radius);
  text-align: center;
}

.dark-mode .ad-space {
  background-color: #2a2a2a;
}

.ad-space img {
  max-width: 100%;
  height: auto;
}

/* Advertisement Containers */
.ad-container {
  width: 100%;
  margin: 20px 0;
  text-align: center;
}

.ad-top-page {
  margin-top: 0;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.ad-middle-page {
  margin: 40px 0;
  padding: 20px 0;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: var(--border-radius);
}

.ad-bottom-page {
  margin-bottom: 0;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.dark-mode .ad-top-page,
.dark-mode .ad-middle-page,
.dark-mode .ad-bottom-page {
  background-color: #2a2a2a;
  border-color: #444;
}

.ad-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dark-mode .ad-title {
  color: #aaa;
}

/* Widget Styles */
.widget {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
}

.dark-mode .widget {
  background-color: #2a2a2a;
  box-shadow: var(--shadow-dark);
}

.widget-title {
  padding: 15px 20px;
  margin: 0;
  background-color: var(--light-primary);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.dark-mode .widget-title {
  background-color: var(--dark-primary);
}

.widget-content {
  padding: 20px;
}

.widget-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.widget-content li {
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.dark-mode .widget-content li {
  border-color: #444;
}

.widget-content li:last-child {
  border-bottom: none;
}

.widget-content a {
  color: var(--light-text);
  transition: var(--transition);
}

.dark-mode .widget-content a {
  color: var(--dark-text);
}

.widget-content a:hover {
  color: var(--light-primary);
}

/* Logo Widget Styles */
.logo-widget {
  position: relative;
}

.logo-section .widget {
  background: none;
  box-shadow: none;
  margin: 0;
}

.logo-section .widget-content {
  padding: 0;
}

.logo-section img {
  max-height: 60px;
  width: auto;
}

.default-logo {
  display: block;
}

.logo-section:not(:empty) + .default-logo {
  display: none;
}

/* Blog Post Styles */
.blog-posts {
  margin: 20px 0;
}

.post {
  margin-bottom: 40px;
  padding: 20px;
  background-color: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.dark-mode .post {
  background-color: #2a2a2a;
  box-shadow: var(--shadow-dark);
}

.post-title {
  margin-bottom: 10px;
}

.post-title a {
  color: var(--light-text);
  text-decoration: none;
}

.dark-mode .post-title a {
  color: var(--dark-text);
}

.post-title a:hover {
  color: var(--light-primary);
}

.post-meta {
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

.dark-mode .post-meta {
  color: #aaa;
}

.post-meta span {
  margin-right: 15px;
}

.post-content {
  line-height: 1.6;
  margin-bottom: 15px;
}

.post-footer {
  text-align: right;
}

.post-footer .read-more {
  display: inline-block;
  padding: 8px 16px;
  background-color: var(--light-primary);
  color: #fff;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-size: 14px;
  transition: var(--transition);
}

.post-footer .read-more:hover {
  background-color: var(--dark-primary);
}

/* Responsive Design */
@media (max-width: 992px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    background-color: var(--light-primary);
    width: 80%;
    height: calc(100vh - 70px);
    padding: 20px;
    transition: var(--transition);
    z-index: 999;
  }

  .nav-menu ul {
    flex-direction: column;
  }

  .dark-mode .nav-menu {
    background-color: var(--dark-primary);
  }

  .nav-menu.active {
    left: 0;
  }
  
  .nav-item {
    margin: 0 0 15px 0;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .match-teams {
    flex-direction: column;
  }
  
  .team {
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .header-inner {
    flex-wrap: wrap;
  }
  
  .settings-bar {
    width: 100%;
    justify-content: flex-end;
    margin-top: 10px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .news-grid, .channels-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .calendar-nav {
    flex-direction: column;
  }
  
  .calendar-day {
    margin-bottom: 10px;
  }
  
  .match-item {
    flex-direction: column;
  }
  
  .match-info {
    margin: 15px 0;
  }
}
]]></b:skin>

  <!-- JavaScript -->
  <script type='text/javascript'>
    //<![CDATA[
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize theme
      const savedTheme = localStorage.getItem('theme') || 'light-mode';
      document.body.className = savedTheme;
      
      // Initialize language
      const savedLang = localStorage.getItem('language') || 'fr';
      document.documentElement.lang = savedLang;
      
      // Theme toggle functionality
      const themeToggle = document.getElementById('theme-toggle');
      if (themeToggle) {
        themeToggle.addEventListener('click', function() {
          if (document.body.classList.contains('light-mode')) {
            document.body.classList.replace('light-mode', 'dark-mode');
            localStorage.setItem('theme', 'dark-mode');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
          } else {
            document.body.classList.replace('dark-mode', 'light-mode');
            localStorage.setItem('theme', 'light-mode');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
          }
        });
        
        // Set initial icon based on theme
        if (document.body.classList.contains('dark-mode')) {
          themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
          themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }
      }
      
      // Language selector functionality
      const langSelector = document.getElementById('lang-selector');
      if (langSelector) {
        langSelector.value = savedLang;
        langSelector.addEventListener('change', function() {
          const selectedLang = this.value;
          document.documentElement.lang = selectedLang;
          localStorage.setItem('language', selectedLang);
          translatePage(selectedLang);
        });
        
        // Initial translation
        translatePage(savedLang);
      }
      
      // Mobile menu toggle
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const navMenu = document.querySelector('.nav-menu');
      
      if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
          navMenu.classList.toggle('active');
          mobileMenuToggle.innerHTML = navMenu.classList.contains('active') ? 
            '<i class="fas fa-times"></i>' : '<i class="fas fa-bars"></i>';
        });
      }
      
      // Tab navigation
      const navLinks = document.querySelectorAll('.nav-link');
      const tabContents = document.querySelectorAll('.tab-content');
      
      navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          
          // Remove active class from all links and tabs
          navLinks.forEach(link => link.classList.remove('active'));
          tabContents.forEach(tab => tab.classList.remove('active'));
          
          // Add active class to clicked link
          this.classList.add('active');
          
          // Show corresponding tab content
          const tabId = this.getAttribute('data-tab');
          document.getElementById(tabId).classList.add('active');
          
          // Close mobile menu if open
          if (navMenu && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            if (mobileMenuToggle) {
              mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
            }
          }
        });
      });
      
      // Activate default tab (Home)
      const defaultTab = document.querySelector('.nav-link[data-tab="home-tab"]');
      if (defaultTab) {
        defaultTab.click();
      }
      
      // Match calendar functionality
      const calendarDays = document.querySelectorAll('.calendar-day');
      const matchLists = document.querySelectorAll('.match-list');
      
      calendarDays.forEach(day => {
        day.addEventListener('click', function() {
          // Remove active class from all days
          calendarDays.forEach(d => d.classList.remove('active'));
          
          // Add active class to clicked day
          this.classList.add('active');
          
          // Show corresponding match list
          const dayId = this.getAttribute('data-day');
          matchLists.forEach(list => list.style.display = 'none');
          document.getElementById(dayId + '-matches').style.display = 'block';
        });
      });
      
      // Activate default day (Today)
      const todayTab = document.querySelector('.calendar-day.today');
      if (todayTab) {
        todayTab.click();
      }
      
      // League statistics selector
      const leagueSelector = document.getElementById('league-selector');
      const leagueTables = document.querySelectorAll('.league-table');
      
      if (leagueSelector) {
        leagueSelector.addEventListener('change', function() {
          const selectedLeague = this.value;
          leagueTables.forEach(table => table.style.display = 'none');
          document.getElementById(selectedLeague + '-table').style.display = 'block';
        });
        
        // Show default league table
        if (leagueSelector.value) {
          document.getElementById(leagueSelector.value + '-table').style.display = 'block';
        }
      }
      
      // Top scorers selector
      const scorersSelector = document.getElementById('scorers-selector');
      const scorersTables = document.querySelectorAll('.scorers-table');
      
      if (scorersSelector) {
        scorersSelector.addEventListener('change', function() {
          const selectedLeague = this.value;
          scorersTables.forEach(table => table.style.display = 'none');
          document.getElementById(selectedLeague + '-scorers').style.display = 'block';
        });
        
        // Show default scorers table
        if (scorersSelector.value) {
          document.getElementById(scorersSelector.value + '-scorers').style.display = 'block';
        }
      }
      
      // Video player selector
      const playerSelector = document.getElementById('player-selector');
      const videoPlayers = document.querySelectorAll('.video-player');
      
      if (playerSelector) {
        playerSelector.addEventListener('change', function() {
          const selectedPlayer = this.value;
          videoPlayers.forEach(player => player.style.display = 'none');
          document.getElementById(selectedPlayer + '-player').style.display = 'block';
        });
        
        // Show default video player
        if (playerSelector.value) {
          document.getElementById(playerSelector.value + '-player').style.display = 'block';
        }
      }
      
      // Lazy loading for images
      const lazyImages = document.querySelectorAll('.lazy-load');
      
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              img.src = img.dataset.src;
              img.classList.add('loaded');
              observer.unobserve(img);
            }
          });
        });
        
        lazyImages.forEach(img => imageObserver.observe(img));
      } else {
        // Fallback for browsers that don't support IntersectionObserver
        lazyImages.forEach(img => {
          img.src = img.dataset.src;
          img.classList.add('loaded');
        });
      }
      
      // Translation functionality
      function translatePage(lang) {
        const translations = {
          'fr': {
            'home': 'Accueil',
            'news': 'Actualités',
            'stats': 'Statistiques',
            'videos': 'Vidéos',
            'live': 'Direct',
            'channels': 'Chaînes',
            'yesterday': 'Hier',
            'today': 'Aujourd\'hui',
            'tomorrow': 'Demain',
            'not_started': 'Pas encore commencé',
            'soon': 'Bientôt',
            'live_match': 'En cours',
            'ended': 'Fin du match',
            'standings': 'Classements',
            'top_scorers': 'Meilleurs buteurs',
            'team': 'Équipe',
            'played': 'J',
            'won': 'G',
            'drawn': 'N',
            'lost': 'P',
            'goals_for': 'BP',
            'goals_against': 'BC',
            'goal_difference': 'DB',
            'points': 'Pts',
            'player': 'Joueur',
            'goals': 'Buts',
            'read_more': 'Lire plus',
            'important': 'IMPORTANT',
            'contact': 'Contact',
            'follow_us': 'Suivez-nous',
            'copyright': 'Tous droits réservés à H-A-2025'
          },
          'en': {
            'home': 'Home',
            'news': 'News',
            'stats': 'Statistics',
            'videos': 'Videos',
            'live': 'Live',
            'channels': 'Channels',
            'yesterday': 'Yesterday',
            'today': 'Today',
            'tomorrow': 'Tomorrow',
            'not_started': 'Not started',
            'soon': 'Coming soon',
            'live_match': 'Live',
            'ended': 'Ended',
            'standings': 'Standings',
            'top_scorers': 'Top Scorers',
            'team': 'Team',
            'played': 'P',
            'won': 'W',
            'drawn': 'D',
            'lost': 'L',
            'goals_for': 'GF',
            'goals_against': 'GA',
            'goal_difference': 'GD',
            'points': 'Pts',
            'player': 'Player',
            'goals': 'Goals',
            'read_more': 'Read more',
            'important': 'IMPORTANT',
            'contact': 'Contact',
            'follow_us': 'Follow us',
            'copyright': 'All rights reserved to H-A-2025'
          },
          'ar': {
            'home': 'الرئيسية',
            'news': 'الأخبار',
            'stats': 'الإحصائيات',
            'videos': 'الفيديوهات',
            'live': 'مباشر',
            'channels': 'القنوات',
            'yesterday': 'أمس',
            'today': 'اليوم',
            'tomorrow': 'غداً',
            'not_started': 'لم تبدأ بعد',
            'soon': 'قريباً',
            'live_match': 'مباشر',
            'ended': 'انتهت',
            'standings': 'الترتيب',
            'top_scorers': 'أفضل الهدافين',
            'team': 'الفريق',
            'played': 'لعب',
            'won': 'فوز',
            'drawn': 'تعادل',
            'lost': 'خسارة',
            'goals_for': 'أهداف له',
            'goals_against': 'أهداف عليه',
            'goal_difference': 'فارق الأهداف',
            'points': 'النقاط',
            'player': 'اللاعب',
            'goals': 'الأهداف',
            'read_more': 'اقرأ المزيد',
            'important': 'مهم',
            'contact': 'اتصل بنا',
            'follow_us': 'تابعنا',
            'copyright': 'جميع الحقوق محفوظة لـ H-A-2025'
          }
        };

        const elementsToTranslate = document.querySelectorAll('[data-translate]');
        elementsToTranslate.forEach(element => {
          const key = element.getAttribute('data-translate');
          if (translations[lang] && translations[lang][key]) {
            element.textContent = translations[lang][key];
          }
        });

        // Update document direction for Arabic
        if (lang === 'ar') {
          document.documentElement.dir = 'rtl';
        } else {
          document.documentElement.dir = 'ltr';
        }
      }

      // Update current time and date
      function updateDateTime() {
        const now = new Date();
        const timeElements = document.querySelectorAll('.current-time');
        const dateElements = document.querySelectorAll('.current-date');

        timeElements.forEach(element => {
          element.textContent = now.toLocaleTimeString();
        });

        dateElements.forEach(element => {
          element.textContent = now.toLocaleDateString();
        });
      }

      // Update time every second
      setInterval(updateDateTime, 1000);
      updateDateTime();

      // Simulate live match updates
      function updateLiveMatches() {
        const liveMatches = document.querySelectorAll('.match-status.live');
        liveMatches.forEach(match => {
          // Simulate score updates (this would be replaced with real API calls)
          const scoreElement = match.closest('.match-item').querySelector('.match-score');
          if (scoreElement && Math.random() < 0.1) { // 10% chance of score update
            const currentScore = scoreElement.textContent.split(' - ');
            if (currentScore.length === 2) {
              const homeScore = parseInt(currentScore[0]) || 0;
              const awayScore = parseInt(currentScore[1]) || 0;

              if (Math.random() < 0.5) {
                scoreElement.textContent = `${homeScore + 1} - ${awayScore}`;
              } else {
                scoreElement.textContent = `${homeScore} - ${awayScore + 1}`;
              }
            }
          }
        });
      }

      // Update live matches every 30 seconds
      setInterval(updateLiveMatches, 30000);

      // Initialize tooltips
      const tooltipElements = document.querySelectorAll('[data-tooltip]');
      tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
          const tooltip = document.createElement('div');
          tooltip.className = 'tooltip';
          tooltip.textContent = this.getAttribute('data-tooltip');
          document.body.appendChild(tooltip);

          const rect = this.getBoundingClientRect();
          tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
          tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        });

        element.addEventListener('mouseleave', function() {
          const tooltip = document.querySelector('.tooltip');
          if (tooltip) {
            tooltip.remove();
          }
        });
      });

      // Initialize API data fetching
      initializeAPIData();

      // Afficher les informations de démarrage
      console.log('🏈 Sports Streaming Template v2.0');
      console.log('📖 Consultez TROUBLESHOOTING.md pour résoudre les erreurs');
      console.log('⚙️ Consultez API_SETUP.md pour configurer les APIs');
      console.log('📚 Consultez BLOGGER_SETUP.md pour l\'installation');
    });

    // API Configuration and Data Fetching
    const API_CONFIG = {
      // Configuration des APIs sportives
      football: {
        // API-Football (RapidAPI)
        rapidapi: {
          baseUrl: 'https://api-football-v1.p.rapidapi.com/v3',
          headers: {
            'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY_HERE', // Remplacez par votre clé
            'X-RapidAPI-Host': 'api-football-v1.p.rapidapi.com'
          }
        },
        // Football-Data.org
        footballData: {
          baseUrl: 'https://api.football-data.org/v4',
          headers: {
            'X-Auth-Token': 'YOUR_FOOTBALL_DATA_TOKEN_HERE' // Remplacez par votre token
          }
        }
      },
      // Configuration pour d'autres sports
      basketball: {
        rapidapi: {
          baseUrl: 'https://api-basketball.p.rapidapi.com',
          headers: {
            'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY_HERE',
            'X-RapidAPI-Host': 'api-basketball.p.rapidapi.com'
          }
        }
      }
    };

    // Fonction principale d'initialisation des données API
    async function initializeAPIData() {
      // Vérifier si les clés API sont configurées
      const hasValidAPIKeys =
        API_CONFIG.football.rapidapi.headers['X-RapidAPI-Key'] !== 'YOUR_RAPIDAPI_KEY_HERE' &&
        API_CONFIG.football.footballData.headers['X-Auth-Token'] !== 'YOUR_FOOTBALL_DATA_TOKEN_HERE';

      if (!hasValidAPIKeys) {
        console.log('⚠️ Clés API non configurées - Utilisation des données de démonstration');
        console.log('📖 Consultez API_SETUP.md pour configurer les APIs');
        loadDemoData();
        return;
      }

      try {
        console.log('🔄 Chargement des données API...');
        // Charger les données en parallèle
        await Promise.all([
          loadLiveMatches(),
          loadLeagueStandings(),
          loadTopScorers(),
          loadSportsNews(),
          loadUpcomingMatches()
        ]);

        console.log('✅ Toutes les données API ont été chargées avec succès');
      } catch (error) {
        console.error('❌ Erreur lors du chargement des données API:', error);
        console.log('🔄 Basculement vers les données de démonstration');
        // Utiliser les données de démonstration en cas d'erreur
        loadDemoData();
      }
    }

    // Charger les matchs en direct
    async function loadLiveMatches() {
      try {
        const response = await fetch(`${API_CONFIG.football.rapidapi.baseUrl}/fixtures?live=all`, {
          headers: API_CONFIG.football.rapidapi.headers
        });

        if (!response.ok) {
          if (response.status === 403) {
            throw new Error('Clé API invalide ou expirée');
          } else if (response.status === 429) {
            throw new Error('Limite de requêtes API atteinte');
          } else {
            throw new Error(`Erreur API: ${response.status}`);
          }
        }

        const data = await response.json();
        if (data.response && data.response.length > 0) {
          updateLiveMatchesDisplay(data.response);
          console.log('✅ Matchs en direct mis à jour');
        }
      } catch (error) {
        console.warn('⚠️ Erreur chargement matchs en direct:', error.message);
        // Garder les données de démonstration
      }
    }

    // Charger les classements des ligues
    async function loadLeagueStandings() {
      const leagues = [39, 61, 140, 78]; // Premier League, Ligue 1, La Liga, Bundesliga

      for (const leagueId of leagues) {
        try {
          const response = await fetch(
            `${API_CONFIG.football.rapidapi.baseUrl}/standings?league=${leagueId}&season=2024`,
            { headers: API_CONFIG.football.rapidapi.headers }
          );

          if (!response.ok) throw new Error(`Erreur API classement ligue ${leagueId}`);

          const data = await response.json();
          updateLeagueStandingsDisplay(leagueId, data.response[0].league.standings[0]);
        } catch (error) {
          console.error(`Erreur chargement classement ligue ${leagueId}:`, error);
        }
      }
    }

    // Charger les meilleurs buteurs
    async function loadTopScorers() {
      const leagues = [39, 61, 140]; // Premier League, Ligue 1, La Liga

      for (const leagueId of leagues) {
        try {
          const response = await fetch(
            `${API_CONFIG.football.rapidapi.baseUrl}/players/topscorers?league=${leagueId}&season=2024`,
            { headers: API_CONFIG.football.rapidapi.headers }
          );

          if (!response.ok) throw new Error(`Erreur API buteurs ligue ${leagueId}`);

          const data = await response.json();
          updateTopScorersDisplay(leagueId, data.response);
        } catch (error) {
          console.error(`Erreur chargement buteurs ligue ${leagueId}:`, error);
        }
      }
    }

    // Charger les actualités sportives
    async function loadSportsNews() {
      try {
        // Utiliser NewsAPI ou une autre API d'actualités
        const response = await fetch(
          'https://newsapi.org/v2/everything?q=football+sport&language=fr&sortBy=publishedAt&apiKey=YOUR_NEWS_API_KEY'
        );

        if (!response.ok) throw new Error('Erreur API actualités');

        const data = await response.json();
        updateNewsDisplay(data.articles);
      } catch (error) {
        console.error('Erreur chargement actualités:', error);
      }
    }

    // Charger les prochains matchs
    async function loadUpcomingMatches() {
      try {
        const today = new Date().toISOString().split('T')[0];
        const tomorrow = new Date(Date.now() + 86400000).toISOString().split('T')[0];

        const response = await fetch(
          `${API_CONFIG.football.rapidapi.baseUrl}/fixtures?date=${tomorrow}`,
          { headers: API_CONFIG.football.rapidapi.headers }
        );

        if (!response.ok) throw new Error('Erreur API prochains matchs');

        const data = await response.json();
        updateUpcomingMatchesDisplay(data.response);
      } catch (error) {
        console.error('Erreur chargement prochains matchs:', error);
      }
    }

    // Fonctions de mise à jour de l'affichage avec les données API

    // Mettre à jour l'affichage des matchs en direct
    function updateLiveMatchesDisplay(matches) {
      const liveContainer = document.querySelector('#today-matches');
      if (!liveContainer || !matches.length) return;

      // Filtrer les matchs en cours
      const liveMatches = matches.filter(match =>
        match.fixture.status.short === '1H' ||
        match.fixture.status.short === '2H' ||
        match.fixture.status.short === 'HT'
      );

      if (liveMatches.length === 0) return;

      // Remplacer le contenu avec les vrais matchs en direct
      liveContainer.innerHTML = liveMatches.map(match => `
        <li class='match-item'>
          <div class='match-teams'>
            <div class='team'>
              <img alt='${match.teams.home.name}' class='team-logo' src='${match.teams.home.logo}'/>
              <span class='team-name'>${match.teams.home.name}</span>
            </div>
            <div class='match-info'>
              <div class='match-time'>${match.fixture.status.elapsed || 0}'</div>
              <div class='match-score'>${match.goals.home || 0} - ${match.goals.away || 0}</div>
              <span class='match-status live' data-translate='live_match'>En cours</span>
            </div>
            <div class='team'>
              <span class='team-name'>${match.teams.away.name}</span>
              <img alt='${match.teams.away.name}' class='team-logo' src='${match.teams.away.logo}'/>
            </div>
          </div>
        </li>
      `).join('');
    }

    // Mettre à jour l'affichage des classements
    function updateLeagueStandingsDisplay(leagueId, standings) {
      const leagueMap = {
        39: 'premier',
        61: 'ligue1',
        140: 'laliga',
        78: 'bundesliga'
      };

      const tableId = leagueMap[leagueId];
      if (!tableId) return;

      const tableBody = document.querySelector(`#${tableId}-table tbody`);
      if (!tableBody) return;

      tableBody.innerHTML = standings.slice(0, 10).map((team, index) => `
        <tr>
          <td>${team.rank}</td>
          <td>
            <img src='${team.team.logo}' alt='${team.team.name}' style='width: 20px; height: 20px; margin-right: 8px;'/>
            ${team.team.name}
          </td>
          <td>${team.all.played}</td>
          <td>${team.all.win}</td>
          <td>${team.all.draw}</td>
          <td>${team.all.lose}</td>
          <td>${team.all.goals.for}</td>
          <td>${team.all.goals.against}</td>
          <td>${team.goalsDiff > 0 ? '+' : ''}${team.goalsDiff}</td>
          <td><strong>${team.points}</strong></td>
        </tr>
      `).join('');
    }

    // Mettre à jour l'affichage des meilleurs buteurs
    function updateTopScorersDisplay(leagueId, scorers) {
      const leagueMap = {
        39: 'premier',
        61: 'ligue1',
        140: 'laliga'
      };

      const tableId = leagueMap[leagueId];
      if (!tableId) return;

      const tableBody = document.querySelector(`#${tableId}-scorers tbody`);
      if (!tableBody) return;

      tableBody.innerHTML = scorers.slice(0, 10).map((scorer, index) => `
        <tr>
          <td>${index + 1}</td>
          <td>
            <img src='${scorer.player.photo}' alt='${scorer.player.name}' style='width: 20px; height: 20px; border-radius: 50%; margin-right: 8px;'/>
            ${scorer.player.name}
          </td>
          <td>
            <img src='${scorer.statistics[0].team.logo}' alt='${scorer.statistics[0].team.name}' style='width: 20px; height: 20px; margin-right: 8px;'/>
            ${scorer.statistics[0].team.name}
          </td>
          <td><strong>${scorer.statistics[0].goals.total || 0}</strong></td>
        </tr>
      `).join('');
    }

    // Mettre à jour l'affichage des actualités
    function updateNewsDisplay(articles) {
      const newsGrid = document.querySelector('.news-grid');
      if (!newsGrid || !articles.length) return;

      newsGrid.innerHTML = articles.slice(0, 6).map(article => `
        <article class='news-card'>
          <img alt='${article.title}' class='news-image' src='${article.urlToImage || 'https://via.placeholder.com/300x180/0066CC/FFFFFF?text=Sports+News'}'/>
          <div class='news-content'>
            <div class='news-date'>${new Date(article.publishedAt).toLocaleDateString('fr-FR')}</div>
            <h3 class='news-title'>${article.title}</h3>
            <p class='news-excerpt'>${article.description || 'Aucune description disponible...'}</p>
            <a class='read-more' href='${article.url}' target='_blank' data-translate='read_more'>Lire plus</a>
          </div>
        </article>
      `).join('');
    }

    // Mettre à jour l'affichage des prochains matchs
    function updateUpcomingMatchesDisplay(matches) {
      const tomorrowContainer = document.querySelector('#tomorrow-matches');
      if (!tomorrowContainer || !matches.length) return;

      tomorrowContainer.innerHTML = matches.slice(0, 5).map(match => `
        <li class='match-item'>
          <div class='match-teams'>
            <div class='team'>
              <img alt='${match.teams.home.name}' class='team-logo' src='${match.teams.home.logo}'/>
              <span class='team-name'>${match.teams.home.name}</span>
            </div>
            <div class='match-info'>
              <div class='match-time'>${new Date(match.fixture.date).toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}</div>
              <div class='match-score'>- - -</div>
              <span class='match-status not-started' data-translate='not_started'>Pas encore commencé</span>
            </div>
            <div class='team'>
              <span class='team-name'>${match.teams.away.name}</span>
              <img alt='${match.teams.away.name}' class='team-logo' src='${match.teams.away.logo}'/>
            </div>
          </div>
        </li>
      `).join('');
    }

    // Charger les données de démonstration en cas d'erreur API
    function loadDemoData() {
      console.log('Chargement des données de démonstration...');
      // Les données de démonstration sont déjà présentes dans le HTML
      // Cette fonction peut être étendue pour charger des données locales
    }

    // Fonction utilitaire pour gérer les erreurs de CORS
    function handleCORSError(url, options) {
      // Utiliser un proxy CORS si nécessaire
      const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
      return fetch(proxyUrl + url, options);
    }

    // Fonction pour rafraîchir les données périodiquement
    function startDataRefresh() {
      // Vérifier si les APIs sont configurées avant de démarrer le rafraîchissement
      const hasValidAPIKeys =
        API_CONFIG.football.rapidapi.headers['X-RapidAPI-Key'] !== 'YOUR_RAPIDAPI_KEY_HERE';

      if (!hasValidAPIKeys) {
        console.log('ℹ️ Rafraîchissement automatique désactivé - APIs non configurées');
        return;
      }

      console.log('🔄 Démarrage du rafraîchissement automatique des données');

      // Rafraîchir les matchs en direct toutes les 2 minutes (au lieu de 30 secondes)
      setInterval(() => {
        loadLiveMatches();
      }, 120000);

      // Rafraîchir les autres données toutes les 10 minutes (au lieu de 5)
      setInterval(() => {
        loadLeagueStandings();
        loadTopScorers();
      }, 600000);

      // Rafraîchir les actualités toutes les 30 minutes (au lieu de 10)
      setInterval(() => {
        loadSportsNews();
      }, 1800000);
    }

    // Démarrer le rafraîchissement automatique des données après 10 secondes
    setTimeout(startDataRefresh, 10000);

    // Service Worker for offline functionality
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        // Essayer d'abord le chemin relatif, puis le chemin absolu
        const swPaths = ['/sw.js', './sw.js', 'sw.js'];

        async function registerServiceWorker() {
          for (const path of swPaths) {
            try {
              const registration = await navigator.serviceWorker.register(path);
              console.log('✅ ServiceWorker enregistré avec succès:', path);
              return;
            } catch (error) {
              console.warn(`⚠️ Échec d'enregistrement SW pour ${path}:`, error.message);
            }
          }
          console.log('ℹ️ ServiceWorker non disponible - Fonctionnement en ligne uniquement');
        }

        registerServiceWorker();
      });
    } else {
      console.log('ℹ️ ServiceWorker non supporté par ce navigateur');
    }
    //]]>
  </script>
</head>

<body class='light-mode'>
  <!-- Header -->
  <header class='header'>
    <div class='container'>
      <div class='header-inner'>
        <div class='logo'>
          <!-- Logo sera remplacé par le widget d'image -->
          <div class='logo-widget'>
            <b:section class='logo-section' id='logo-section' maxwidgets='1' showaddelement='yes'>
              <!-- Le widget d'image sera ajouté ici via l'interface Blogger -->
            </b:section>
            <!-- Logo par défaut si aucun widget n'est configuré -->
            <div class='default-logo'>
              <img alt='Sports Streaming' src='https://via.placeholder.com/120x40/0000FF/FFFFFF?text=SPORTS'/>
            </div>
          </div>
        </div>

        <nav class='nav-menu' id='nav-menu'>
          <ul>
            <li class='nav-item'>
              <a class='nav-link' data-tab='home-tab' data-translate='home' href='#'>Accueil</a>
            </li>
            <li class='nav-item'>
              <a class='nav-link' data-tab='news-tab' data-translate='news' href='#'>Actualités</a>
            </li>
            <li class='nav-item'>
              <a class='nav-link' data-tab='stats-tab' data-translate='stats' href='#'>Statistiques</a>
            </li>
            <li class='nav-item'>
              <a class='nav-link' data-tab='videos-tab' data-translate='videos' href='#'>Vidéos</a>
            </li>
            <li class='nav-item'>
              <a class='nav-link' data-tab='live-tab' data-translate='live' href='#'>Direct</a>
            </li>
            <li class='nav-item'>
              <a class='nav-link' data-tab='channels-tab' data-translate='channels' href='#'>Chaînes</a>
            </li>
          </ul>
        </nav>

        <div class='settings-bar'>
          <button class='theme-toggle' id='theme-toggle' title='Changer le thème'>
            <i class='fas fa-moon'></i>
          </button>
          <div class='lang-selector'>
            <select id='lang-selector'>
              <option value='fr'>Français</option>
              <option value='en'>English</option>
              <option value='ar'>العربية</option>
            </select>
          </div>
          <button class='mobile-menu-toggle' id='mobile-menu-toggle'>
            <i class='fas fa-bars'></i>
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- News Ticker -->
  <div class='news-ticker'>
    <div class='container'>
      <span class='news-ticker-label' data-translate='important'>IMPORTANT</span>
      <span class='news-ticker-content'>
        🔴 Manchester United vs Liverpool - 20:00 •
        ⚽ Real Madrid remporte la Champions League •
        🏆 PSG signe Mbappé pour 3 ans •
        📺 Nouveau partenariat avec beIN Sports
      </span>
    </div>
  </div>

  <!-- Top Advertisement -->
  <div class='ad-container ad-top-page'>
    <b:section class='top-page-ads' id='top-page-ads' maxwidgets='2' showaddelement='yes'>
      <!-- Publicité haut de page sera ajoutée ici -->
    </b:section>
  </div>

  <!-- Main Content -->
  <main class='main-content'>
    <div class='container'>

      <!-- Home Tab -->
      <div class='tab-content' id='home-tab'>
        <!-- Match Calendar -->
        <div class='match-calendar'>
          <h2>Calendrier des Matchs</h2>
          <div class='calendar-nav'>
            <div class='calendar-day yesterday' data-day='yesterday' data-translate='yesterday'>Hier</div>
            <div class='calendar-day today' data-day='today' data-translate='today'>Aujourd'hui</div>
            <div class='calendar-day tomorrow' data-day='tomorrow' data-translate='tomorrow'>Demain</div>
          </div>

          <!-- Yesterday's Matches -->
          <ul class='match-list' id='yesterday-matches' style='display: none;'>
            <li class='match-item'>
              <div class='match-teams'>
                <div class='team'>
                  <img alt='Real Madrid' class='team-logo' src='https://via.placeholder.com/30x30/FFFFFF/000000?text=RM' onerror='this.src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'30\' height=\'30\' viewBox=\'0 0 30 30\'%3E%3Crect width=\'30\' height=\'30\' fill=\'%23FFFFFF\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' text-anchor=\'middle\' dy=\'0.3em\' fill=\'%23000000\' font-size=\'10\'%3ERM%3C/text%3E%3C/svg%3E"'/>
                  <span class='team-name'>Real Madrid</span>
                </div>
                <div class='match-info'>
                  <div class='match-time'>21:00</div>
                  <div class='match-score'>2 - 1</div>
                  <span class='match-status ended' data-translate='ended'>Fin du match</span>
                </div>
                <div class='team'>
                  <span class='team-name'>Barcelona</span>
                  <img alt='Barcelona' class='team-logo' src='https://via.placeholder.com/30x30/FF0000/FFFFFF?text=BAR'/>
                </div>
              </div>
            </li>
            <li class='match-item'>
              <div class='match-teams'>
                <div class='team'>
                  <img alt='Manchester United' class='team-logo' src='https://via.placeholder.com/30x30/FF0000/FFFFFF?text=MU'/>
                  <span class='team-name'>Manchester United</span>
                </div>
                <div class='match-info'>
                  <div class='match-time'>18:30</div>
                  <div class='match-score'>1 - 3</div>
                  <span class='match-status ended' data-translate='ended'>Fin du match</span>
                </div>
                <div class='team'>
                  <span class='team-name'>Liverpool</span>
                  <img alt='Liverpool' class='team-logo' src='https://via.placeholder.com/30x30/FF0000/FFFFFF?text=LIV'/>
                </div>
              </div>
            </li>
          </ul>

          <!-- Today's Matches -->
          <ul class='match-list' id='today-matches'>
            <li class='match-item'>
              <div class='match-teams'>
                <div class='team'>
                  <img alt='PSG' class='team-logo' src='https://via.placeholder.com/30x30/0000FF/FFFFFF?text=PSG'/>
                  <span class='team-name'>Paris Saint-Germain</span>
                </div>
                <div class='match-info'>
                  <div class='match-time'>20:00</div>
                  <div class='match-score'>1 - 1</div>
                  <span class='match-status live' data-translate='live_match'>En cours</span>
                </div>
                <div class='team'>
                  <span class='team-name'>Olympique de Marseille</span>
                  <img alt='OM' class='team-logo' src='https://via.placeholder.com/30x30/00BFFF/FFFFFF?text=OM'/>
                </div>
              </div>
            </li>
            <li class='match-item'>
              <div class='match-teams'>
                <div class='team'>
                  <img alt='Chelsea' class='team-logo' src='https://via.placeholder.com/30x30/0000FF/FFFFFF?text=CHE'/>
                  <span class='team-name'>Chelsea</span>
                </div>
                <div class='match-info'>
                  <div class='match-time'>22:30</div>
                  <div class='match-score'>- - -</div>
                  <span class='match-status soon' data-translate='soon'>Bientôt</span>
                </div>
                <div class='team'>
                  <span class='team-name'>Arsenal</span>
                  <img alt='Arsenal' class='team-logo' src='https://via.placeholder.com/30x30/FF0000/FFFFFF?text=ARS'/>
                </div>
              </div>
            </li>
          </ul>

          <!-- Tomorrow's Matches -->
          <ul class='match-list' id='tomorrow-matches' style='display: none;'>
            <li class='match-item'>
              <div class='match-teams'>
                <div class='team'>
                  <img alt='Bayern Munich' class='team-logo' src='https://via.placeholder.com/30x30/FF0000/FFFFFF?text=BAY'/>
                  <span class='team-name'>Bayern Munich</span>
                </div>
                <div class='match-info'>
                  <div class='match-time'>19:00</div>
                  <div class='match-score'>- - -</div>
                  <span class='match-status not-started' data-translate='not_started'>Pas encore commencé</span>
                </div>
                <div class='team'>
                  <span class='team-name'>Borussia Dortmund</span>
                  <img alt='Dortmund' class='team-logo' src='https://via.placeholder.com/30x30/FFFF00/000000?text=BVB'/>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <!-- Ad Space -->
        <div class='ad-space'>
          <div style='width: 728px; max-width: 100%; height: 90px; background: linear-gradient(135deg, #CCCCCC, #999999); display: flex; align-items: center; justify-content: center; color: #666666; font-weight: bold; border-radius: 8px; margin: 0 auto;'>
            Espace Publicitaire 728x90
          </div>
        </div>
      </div>

      <!-- Middle Advertisement -->
      <div class='ad-container ad-middle-page'>
        <b:section class='middle-page-ads' id='middle-page-ads' maxwidgets='3' showaddelement='yes'>
          <!-- Publicité milieu de page sera ajoutée ici -->
        </b:section>
      </div>

      <!-- News Tab -->
      <div class='tab-content' id='news-tab'>
        <h2 data-translate='news'>Actualités</h2>
        <div class='news-grid'>
          <article class='news-card'>
            <img alt='News 1' class='news-image lazy-load' data-src='https://via.placeholder.com/300x180/0066CC/FFFFFF?text=Football+News'/>
            <div class='news-content'>
              <div class='news-date'>15 Décembre 2024</div>
              <h3 class='news-title'>Real Madrid remporte le Clasico face à Barcelone</h3>
              <p class='news-excerpt'>Une victoire spectaculaire 2-1 au Santiago Bernabéu devant 80 000 spectateurs...</p>
              <a class='read-more' data-translate='read_more' href='#'>Lire plus</a>
            </div>
          </article>

          <article class='news-card'>
            <img alt='News 2' class='news-image lazy-load' data-src='https://via.placeholder.com/300x180/FF6600/FFFFFF?text=Transfer+News'/>
            <div class='news-content'>
              <div class='news-date'>14 Décembre 2024</div>
              <h3 class='news-title'>Mbappé prolonge son contrat avec le PSG</h3>
              <p class='news-excerpt'>L'attaquant français signe un nouveau contrat de 3 ans avec le club parisien...</p>
              <a class='read-more' data-translate='read_more' href='#'>Lire plus</a>
            </div>
          </article>

          <article class='news-card'>
            <img alt='News 3' class='news-image lazy-load' data-src='https://via.placeholder.com/300x180/009900/FFFFFF?text=Champions+League'/>
            <div class='news-content'>
              <div class='news-date'>13 Décembre 2024</div>
              <h3 class='news-title'>Tirage au sort des huitièmes de finale de la Champions League</h3>
              <p class='news-excerpt'>Les affiches prometteuses pour la phase à élimination directe...</p>
              <a class='read-more' data-translate='read_more' href='#'>Lire plus</a>
            </div>
          </article>
        </div>
      </div>

      <!-- Statistics Tab -->
      <div class='tab-content' id='stats-tab'>
        <h2 data-translate='stats'>Statistiques</h2>

        <!-- League Standings -->
        <div class='league-stats'>
          <h3 data-translate='standings'>Classements</h3>
          <div class='stats-selector'>
            <select id='league-selector'>
              <option value='ligue1'>Ligue 1</option>
              <option value='premier'>Premier League</option>
              <option value='laliga'>La Liga</option>
              <option value='bundesliga'>Bundesliga</option>
            </select>
          </div>

          <!-- Ligue 1 Table -->
          <div class='league-table' id='ligue1-table'>
            <table class='standings-table'>
              <thead>
                <tr>
                  <th>#</th>
                  <th data-translate='team'>Équipe</th>
                  <th data-translate='played'>J</th>
                  <th data-translate='won'>G</th>
                  <th data-translate='drawn'>N</th>
                  <th data-translate='lost'>P</th>
                  <th data-translate='goals_for'>BP</th>
                  <th data-translate='goals_against'>BC</th>
                  <th data-translate='goal_difference'>DB</th>
                  <th data-translate='points'>Pts</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1</td>
                  <td>Paris Saint-Germain</td>
                  <td>15</td>
                  <td>12</td>
                  <td>2</td>
                  <td>1</td>
                  <td>42</td>
                  <td>12</td>
                  <td>+30</td>
                  <td>38</td>
                </tr>
                <tr>
                  <td>2</td>
                  <td>Olympique de Marseille</td>
                  <td>15</td>
                  <td>10</td>
                  <td>3</td>
                  <td>2</td>
                  <td>28</td>
                  <td>15</td>
                  <td>+13</td>
                  <td>33</td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>AS Monaco</td>
                  <td>15</td>
                  <td>9</td>
                  <td>4</td>
                  <td>2</td>
                  <td>25</td>
                  <td>14</td>
                  <td>+11</td>
                  <td>31</td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Premier League Table -->
          <div class='league-table' id='premier-table' style='display: none;'>
            <table class='standings-table'>
              <thead>
                <tr>
                  <th>#</th>
                  <th data-translate='team'>Team</th>
                  <th data-translate='played'>P</th>
                  <th data-translate='won'>W</th>
                  <th data-translate='drawn'>D</th>
                  <th data-translate='lost'>L</th>
                  <th data-translate='goals_for'>GF</th>
                  <th data-translate='goals_against'>GA</th>
                  <th data-translate='goal_difference'>GD</th>
                  <th data-translate='points'>Pts</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1</td>
                  <td>Manchester City</td>
                  <td>15</td>
                  <td>11</td>
                  <td>3</td>
                  <td>1</td>
                  <td>35</td>
                  <td>12</td>
                  <td>+23</td>
                  <td>36</td>
                </tr>
                <tr>
                  <td>2</td>
                  <td>Arsenal</td>
                  <td>15</td>
                  <td>10</td>
                  <td>4</td>
                  <td>1</td>
                  <td>32</td>
                  <td>14</td>
                  <td>+18</td>
                  <td>34</td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>Liverpool</td>
                  <td>15</td>
                  <td>9</td>
                  <td>4</td>
                  <td>2</td>
                  <td>30</td>
                  <td>16</td>
                  <td>+14</td>
                  <td>31</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Top Scorers -->
        <div class='league-stats'>
          <h3 data-translate='top_scorers'>Meilleurs buteurs</h3>
          <div class='stats-selector'>
            <select id='scorers-selector'>
              <option value='ligue1'>Ligue 1</option>
              <option value='premier'>Premier League</option>
              <option value='laliga'>La Liga</option>
            </select>
          </div>

          <!-- Ligue 1 Scorers -->
          <div class='scorers-table' id='ligue1-scorers'>
            <table class='standings-table'>
              <thead>
                <tr>
                  <th>#</th>
                  <th data-translate='player'>Joueur</th>
                  <th data-translate='team'>Équipe</th>
                  <th data-translate='goals'>Buts</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1</td>
                  <td>Kylian Mbappé</td>
                  <td>PSG</td>
                  <td>18</td>
                </tr>
                <tr>
                  <td>2</td>
                  <td>Alexandre Lacazette</td>
                  <td>Lyon</td>
                  <td>12</td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>Wissam Ben Yedder</td>
                  <td>Monaco</td>
                  <td>11</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Videos Tab -->
      <div class='tab-content' id='videos-tab'>
        <h2 data-translate='videos'>Vidéos</h2>

        <!-- Video Player Controls -->
        <div class='player-controls'>
          <select class='player-selector' id='player-selector'>
            <option value='player1'>Lecteur 1</option>
            <option value='player2'>Lecteur 2</option>
            <option value='player3'>Lecteur 3</option>
          </select>
        </div>

        <!-- Video Players -->
        <div class='video-container'>
          <div class='video-player' id='player1-player'>
            <iframe allowfullscreen='true' frameborder='0' src='https://www.youtube.com/embed/dQw4w9WgXcQ'></iframe>
          </div>
          <div class='video-player' id='player2-player' style='display: none;'>
            <iframe allowfullscreen='true' frameborder='0' src='https://www.youtube.com/embed/dQw4w9WgXcQ'></iframe>
          </div>
          <div class='video-player' id='player3-player' style='display: none;'>
            <iframe allowfullscreen='true' frameborder='0' src='https://www.youtube.com/embed/dQw4w9WgXcQ'></iframe>
          </div>
        </div>

        <!-- Ad Space -->
        <div class='ad-space'>
          <div style='width: 728px; max-width: 100%; height: 90px; background: linear-gradient(135deg, #FF6600, #FF8800); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; border-radius: 8px; margin: 0 auto;'>
            Publicité Vidéo 728x90
          </div>
        </div>
      </div>

      <!-- Live Tab -->
      <div class='tab-content' id='live-tab'>
        <h2 data-translate='live'>Direct</h2>

        <!-- Live Match Info -->
        <div class='live-match-info'>
          <h3>🔴 Match en Direct</h3>
          <div class='match-item'>
            <div class='match-teams'>
              <div class='team'>
                <img alt='PSG' class='team-logo' src='https://via.placeholder.com/30x30/0000FF/FFFFFF?text=PSG'/>
                <span class='team-name'>Paris Saint-Germain</span>
              </div>
              <div class='match-info'>
                <div class='match-time'>75'</div>
                <div class='match-score'>2 - 1</div>
                <span class='match-status live' data-translate='live_match'>En cours</span>
              </div>
              <div class='team'>
                <span class='team-name'>Olympique de Marseille</span>
                <img alt='OM' class='team-logo' src='https://via.placeholder.com/30x30/00BFFF/FFFFFF?text=OM'/>
              </div>
            </div>
          </div>
        </div>

        <!-- Live Stream -->
        <div class='video-container'>
          <iframe allowfullscreen='true' frameborder='0' src='https://www.youtube.com/embed/live_stream?channel=UCexample'></iframe>
        </div>

        <!-- Live Stats -->
        <div class='live-stats'>
          <h4>Statistiques du Match</h4>
          <div class='stats-grid'>
            <div class='stat-item'>
              <span class='stat-label'>Possession</span>
              <div class='stat-bar'>
                <div class='stat-value' style='width: 60%;'>60%</div>
              </div>
              <span class='stat-value'>40%</span>
            </div>
            <div class='stat-item'>
              <span class='stat-label'>Tirs</span>
              <span class='stat-value'>8</span>
              <span class='stat-value'>5</span>
            </div>
            <div class='stat-item'>
              <span class='stat-label'>Tirs cadrés</span>
              <span class='stat-value'>4</span>
              <span class='stat-value'>2</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Channels Tab -->
      <div class='tab-content' id='channels-tab'>
        <h2 data-translate='channels'>Chaînes TV</h2>

        <div class='channels-grid'>
          <div class='channel-card'>
            <img alt='beIN Sports 1' class='channel-logo' src='https://via.placeholder.com/200x120/FF0000/FFFFFF?text=beIN+Sports+1'/>
            <div class='channel-info'>
              <h3 class='channel-name'>beIN Sports 1</h3>
              <p class='channel-description'>Chaîne premium pour le football international</p>
            </div>
          </div>

          <div class='channel-card'>
            <img alt='Canal+ Sport' class='channel-logo' src='https://via.placeholder.com/200x120/000000/FFFFFF?text=Canal%2B+Sport'/>
            <div class='channel-info'>
              <h3 class='channel-name'>Canal+ Sport</h3>
              <p class='channel-description'>Sports en direct et programmes exclusifs</p>
            </div>
          </div>

          <div class='channel-card'>
            <img alt='Eurosport 1' class='channel-logo' src='https://via.placeholder.com/200x120/0066CC/FFFFFF?text=Eurosport+1'/>
            <div class='channel-info'>
              <h3 class='channel-name'>Eurosport 1</h3>
              <p class='channel-description'>Le meilleur du sport européen</p>
            </div>
          </div>

          <div class='channel-card'>
            <img alt='RMC Sport 1' class='channel-logo' src='https://via.placeholder.com/200x120/FF6600/FFFFFF?text=RMC+Sport+1'/>
            <div class='channel-info'>
              <h3 class='channel-name'>RMC Sport 1</h3>
              <p class='channel-description'>Champions League et sports premium</p>
            </div>
          </div>

          <div class='channel-card'>
            <img alt='L&apos;Équipe' class='channel-logo' src='https://via.placeholder.com/200x120/009900/FFFFFF?text=L%27Équipe'/>
            <div class='channel-info'>
              <h3 class='channel-name'>L'Équipe</h3>
              <p class='channel-description'>Actualités sportives 24h/24</p>
            </div>
          </div>

          <div class='channel-card'>
            <img alt='Sky Sports' class='channel-logo' src='https://via.placeholder.com/200x120/0000FF/FFFFFF?text=Sky+Sports'/>
            <div class='channel-info'>
              <h3 class='channel-name'>Sky Sports</h3>
              <p class='channel-description'>Premier League et sports anglais</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Bottom Advertisement -->
  <div class='ad-container ad-bottom-page'>
    <b:section class='bottom-page-ads' id='bottom-page-ads' maxwidgets='2' showaddelement='yes'>
      <!-- Publicité bas de page sera ajoutée ici -->
    </b:section>
  </div>

  <!-- Footer -->
  <footer class='footer'>
    <div class='container'>
      <div class='footer-content'>
        <div class='footer-section'>
          <h3>À propos</h3>
          <ul class='footer-links'>
            <li><a href='#'>Qui sommes-nous</a></li>
            <li><a href='#'>Notre équipe</a></li>
            <li><a href='#'>Mentions légales</a></li>
            <li><a href='#'>Politique de confidentialité</a></li>
          </ul>
        </div>

        <div class='footer-section'>
          <h3>Sports</h3>
          <ul class='footer-links'>
            <li><a href='#'>Football</a></li>
            <li><a href='#'>Basketball</a></li>
            <li><a href='#'>Tennis</a></li>
            <li><a href='#'>Rugby</a></li>
          </ul>
        </div>

        <div class='footer-section'>
          <h3>Ligues</h3>
          <ul class='footer-links'>
            <li><a href='#'>Ligue 1</a></li>
            <li><a href='#'>Premier League</a></li>
            <li><a href='#'>La Liga</a></li>
            <li><a href='#'>Champions League</a></li>
          </ul>
        </div>

        <div class='footer-section'>
          <h3 data-translate='contact'>Contact</h3>
          <ul class='footer-links'>
            <li><a href='mailto:<EMAIL>'><EMAIL></a></li>
            <li><a href='tel:+33123456789'>+33 1 23 45 67 89</a></li>
            <li><a href='#'>Support technique</a></li>
          </ul>

          <h4 data-translate='follow_us'>Suivez-nous</h4>
          <div class='social-links'>
            <a href='#' title='Facebook'><i class='fab fa-facebook-f'></i></a>
            <a href='#' title='Twitter'><i class='fab fa-twitter'></i></a>
            <a href='#' title='Instagram'><i class='fab fa-instagram'></i></a>
            <a href='#' title='YouTube'><i class='fab fa-youtube'></i></a>
            <a href='#' title='TikTok'><i class='fab fa-tiktok'></i></a>
          </div>
        </div>
      </div>

      <div class='copyright'>
        <p data-translate='copyright'>© 2025 Sports Streaming Platform. Tous droits réservés à H-A-2025</p>
        <p>Développé avec ❤️ pour les passionnés de sport</p>
      </div>
    </div>
  </footer>

  <!-- Additional CSS for missing styles -->
  <style>
    /* Tooltip styles */
    .tooltip {
      position: absolute;
      background-color: #333;
      color: #fff;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1001;
      pointer-events: none;
    }

    /* Live stats styles */
    .live-stats {
      margin-top: 20px;
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 8px;
    }

    .dark-mode .live-stats {
      background-color: #2a2a2a;
    }

    .stats-grid {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .stat-label {
      flex: 1;
      font-weight: 500;
    }

    .stat-bar {
      flex: 2;
      height: 20px;
      background-color: #e0e0e0;
      border-radius: 10px;
      margin: 0 15px;
      position: relative;
      overflow: hidden;
    }

    .dark-mode .stat-bar {
      background-color: #444;
    }

    .stat-bar .stat-value {
      height: 100%;
      background-color: var(--light-primary);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: bold;
      transition: width 0.3s ease;
    }

    .stat-item > .stat-value {
      flex: 0 0 auto;
      font-weight: bold;
      min-width: 30px;
      text-align: center;
    }

    /* Live match info styles */
    .live-match-info {
      margin-bottom: 20px;
    }

    .live-match-info h3 {
      color: var(--accent-color);
      margin-bottom: 15px;
      font-size: 18px;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
      .stats-grid {
        gap: 10px;
      }

      .stat-item {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
      }

      .stat-bar {
        margin: 0;
      }

      .stat-item > .stat-value {
        text-align: left;
      }
    }
  </style>

  <!-- Blogger Widgets Sections -->

  <!-- Header Widgets Section -->
  <b:section class='header-widgets' id='header-widgets' maxwidgets='3' showaddelement='yes'>
    <!-- Logo Upload Widget -->
    <b:widget id='Image1' locked='false' title='Logo du Site' type='Image' version='1' visible='true'>
      <b:includable id='main'>
        <b:if cond='data:title != &quot;&quot;'>
          <h2 class='title'><data:title/></h2>
        </b:if>
        <div class='widget-content'>
          <b:if cond='data:sourceUrl'>
            <a expr:href='data:sourceUrl'>
              <img class='logo-image' expr:alt='data:title' expr:src='data:image' style='max-height: 60px; width: auto;'/>
            </a>
          <b:else/>
            <img class='logo-image' expr:alt='data:title' expr:src='data:image' style='max-height: 60px; width: auto;'/>
          </b:if>
        </div>
      </b:includable>
    </b:widget>
  </b:section>

  <!-- Top Advertisement Section -->
  <b:section class='top-ads' id='top-ads' maxwidgets='2' showaddelement='yes'>
    <b:widget id='HTML1' locked='false' title='Publicité Haut de Page' type='HTML' version='1' visible='true'>
      <b:includable id='main'>
        <div class='ad-space ad-top'>
          <b:if cond='data:title != &quot;&quot;'>
            <h3 class='ad-title'><data:title/></h3>
          </b:if>
          <div class='widget-content'>
            <data:content/>
          </div>
        </div>
      </b:includable>
    </b:widget>
  </b:section>

  <!-- Main Content Section -->
  <b:section class='main' id='main' maxwidgets='1' showaddelement='no'>
    <b:widget id='Blog1' locked='true' title='Articles du Blog' type='Blog' version='1' visible='true'>
      <b:includable id='main'>
        <div class='blog-posts'>
          <b:loop values='data:posts' var='post'>
            <article class='post'>
              <h2 class='post-title'>
                <a expr:href='data:post.url'><data:post.title/></a>
              </h2>
              <div class='post-meta'>
                <span class='post-date'><data:post.dateHeader/></span>
                <span class='post-author'>Par <data:post.author/></span>
              </div>
              <div class='post-content'>
                <data:post.body/>
              </div>
              <div class='post-footer'>
                <a class='read-more' expr:href='data:post.url'>Lire la suite</a>
              </div>
            </article>
          </b:loop>
        </div>
      </b:includable>
    </b:widget>
  </b:section>

  <!-- Middle Advertisement Section -->
  <b:section class='middle-ads' id='middle-ads' maxwidgets='3' showaddelement='yes'>
    <b:widget id='HTML2' locked='false' title='Publicité Milieu de Page' type='HTML' version='1' visible='true'>
      <b:includable id='main'>
        <div class='ad-space ad-middle'>
          <b:if cond='data:title != &quot;&quot;'>
            <h3 class='ad-title'><data:title/></h3>
          </b:if>
          <div class='widget-content'>
            <data:content/>
          </div>
        </div>
      </b:includable>
    </b:widget>
  </b:section>

  <!-- Sidebar Widgets Section -->
  <b:section class='sidebar' id='sidebar' maxwidgets='10' showaddelement='yes'>
    <!-- Popular Posts Widget -->
    <b:widget id='PopularPosts1' locked='false' title='Articles Populaires' type='PopularPosts' version='1' visible='true'>
      <b:includable id='main'>
        <div class='widget popular-posts'>
          <h3 class='widget-title'><data:title/></h3>
          <div class='widget-content'>
            <ul>
              <b:loop values='data:posts' var='post'>
                <li>
                  <a expr:href='data:post.href'><data:post.title/></a>
                  <span class='post-snippet'><data:post.snippet/></span>
                </li>
              </b:loop>
            </ul>
          </div>
        </div>
      </b:includable>
    </b:widget>

    <!-- Archive Widget -->
    <b:widget id='BlogArchive1' locked='false' title='Archives' type='BlogArchive' version='1' visible='true'>
      <b:includable id='main'>
        <div class='widget blog-archive'>
          <h3 class='widget-title'><data:title/></h3>
          <div class='widget-content'>
            <div id='ArchiveList'>
              <div expr:id='data:widget.instanceId + &quot;_ArchiveList&quot;'>
                <b:include data='posts' name='interval'/>
              </div>
            </div>
          </div>
        </div>
      </b:includable>
    </b:widget>

    <!-- Labels Widget -->
    <b:widget id='Label1' locked='false' title='Catégories' type='Label' version='1' visible='true'>
      <b:includable id='main'>
        <div class='widget labels'>
          <h3 class='widget-title'><data:title/></h3>
          <div class='widget-content'>
            <ul>
              <b:loop values='data:labels' var='label'>
                <li>
                  <a expr:href='data:label.url'><data:label.name/></a>
                  <span class='label-count'>(<data:label.count/>)</span>
                </li>
              </b:loop>
            </ul>
          </div>
        </div>
      </b:includable>
    </b:widget>
  </b:section>

  <!-- Bottom Advertisement Section -->
  <b:section class='bottom-ads' id='bottom-ads' maxwidgets='2' showaddelement='yes'>
    <b:widget id='HTML3' locked='false' title='Publicité Bas de Page' type='HTML' version='1' visible='true'>
      <b:includable id='main'>
        <div class='ad-space ad-bottom'>
          <b:if cond='data:title != &quot;&quot;'>
            <h3 class='ad-title'><data:title/></h3>
          </b:if>
          <div class='widget-content'>
            <data:content/>
          </div>
        </div>
      </b:includable>
    </b:widget>
  </b:section>

</body>
</html>
