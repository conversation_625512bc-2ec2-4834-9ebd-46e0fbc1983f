# Sports Streaming Template

Un template moderne et responsive pour une plateforme de streaming sportif, conçu pour Blogger/Blogspot.

## 🚀 Fonctionnalités

### ✨ Interface Utilisateur
- **Design moderne et responsive** - Compatible avec tous les appareils
- **Mode sombre/clair** - Basculement automatique avec sauvegarde des préférences
- **Support multilingue** - Français, Anglais, Arabe avec direction RTL
- **Navigation par onglets** - Interface intuitive et fluide
- **Animations CSS** - Transitions et effets visuels

### 📱 Sections Principales
1. **Accueil** - Calendrier des matchs avec filtres par jour
2. **Actualités** - Articles sportifs avec images et extraits
3. **Statistiques** - Classements et meilleurs buteurs par ligue
4. **Vidéos** - Lecteurs vidéo multiples avec sélecteur
5. **Direct** - Streaming en direct avec statistiques live
6. **Chaînes** - Grille des chaînes TV sportives

### 🎛️ Widgets Blogger Intégrés
- **Widget Logo** - Téléchargement facile depuis l'interface Blogger
- **Widgets Publicitaires** - 6 emplacements stratégiques (haut, milieu, bas de page + sidebar)
- **Articles Populaires** - Affichage automatique des posts les plus lus
- **Archives** - Navigation par date
- **Catégories/Labels** - Organisation du contenu
- **Sidebar Personnalisable** - Jusqu'à 10 widgets supplémentaires

### 🔧 Fonctionnalités Techniques
- **Service Worker** - Fonctionnement hors ligne
- **Lazy Loading** - Chargement optimisé des images
- **SEO optimisé** - Meta tags et structure sémantique
- **Performance** - CSS et JavaScript optimisés
- **Accessibilité** - Support des lecteurs d'écran
- **APIs Sportives** - Intégration avec API-Football, Football-Data.org, NewsAPI
- **Données en Temps Réel** - Mise à jour automatique des scores et classements

## 📋 Installation

### Pour Blogger/Blogspot :
1. Connectez-vous à votre compte Blogger
2. Allez dans **Thème** > **Modifier le code HTML**
3. Copiez le contenu de `sports_streaming_template.xml`
4. Collez-le dans l'éditeur et sauvegardez
5. Uploadez le fichier `sw.js` dans votre hébergement

### Configuration requise :
- Compte Blogger/Blogspot
- Accès à l'édition de template
- (Optionnel) Hébergement pour le Service Worker
- (Optionnel) Clés API pour les données en temps réel

### Configuration des APIs (Optionnel) :
Pour activer les données en temps réel, consultez `API_SETUP.md` :
1. **API-Football** (RapidAPI) - Matchs et statistiques
2. **Football-Data.org** - Classements européens
3. **NewsAPI** - Actualités sportives

Sans APIs, le template fonctionne avec des données de démonstration.

## 🎨 Personnalisation

### Variables CSS :
```css
:root {
  --dark-primary: #000080;    /* Couleur principale sombre */
  --light-primary: #0000FF;   /* Couleur principale claire */
  --accent-color: #FF0000;    /* Couleur d'accent */
  --background-dark: #121212; /* Arrière-plan mode sombre */
  --background-light: #f5f5f5; /* Arrière-plan mode clair */
}
```

### Modification des données :
- **Matchs** : Modifiez les sections `.match-item`
- **Statistiques** : Éditez les tableaux `.standings-table`
- **Actualités** : Personnalisez les cartes `.news-card`
- **Chaînes** : Ajustez la grille `.channels-grid`

## 🌐 Support Multilingue

Le template supporte 3 langues :
- **Français** (par défaut)
- **Anglais**
- **Arabe** (avec support RTL)

### Ajouter une nouvelle langue :
1. Ajoutez l'option dans le sélecteur de langue
2. Étendez l'objet `translations` dans le JavaScript
3. Ajoutez les attributs `data-translate` aux éléments

## 📱 Responsive Design

- **Desktop** : Layout complet avec sidebar
- **Tablet** : Navigation adaptée
- **Mobile** : Menu hamburger et layout vertical

## 🔧 Maintenance

### Mise à jour des données :
- Les scores en direct sont simulés (remplacez par une vraie API)
- Les images utilisent des placeholders (remplacez par de vraies images)
- Les liens sont des exemples (configurez selon vos besoins)

### Performance :
- Optimisez les images avant upload
- Utilisez un CDN pour les ressources statiques
- Activez la compression GZIP

## 🔧 Dépannage

### Erreurs Courantes
Si vous voyez des erreurs dans la console du navigateur (F12), consultez `TROUBLESHOOTING.md` :

- **ERR_NAME_NOT_RESOLVED** - ✅ Corrigé dans cette version
- **API 403/429** - Normal sans clés API configurées
- **ServiceWorker failed** - Optionnel, le site fonctionne sans
- **YouTube/DoubleClick errors** - Normal avec bloqueurs de pub

### Messages Console Normaux ✅
```
⚠️ Clés API non configurées - Utilisation des données de démonstration
📖 Consultez API_SETUP.md pour configurer les APIs
ℹ️ Rafraîchissement automatique désactivé - APIs non configurées
```

## 📞 Support

Pour toute question ou problème :
- **Dépannage** : Consultez `TROUBLESHOOTING.md`
- **Configuration APIs** : Consultez `API_SETUP.md`
- **Installation Blogger** : Consultez `BLOGGER_SETUP.md`
- Créé par : H-A-2025

## 📄 Licence

Ce template est fourni "tel quel" à des fins éducatives et de démonstration.
Libre d'utilisation et de modification selon vos besoins.

---

**Développé avec ❤️ pour les passionnés de sport**
