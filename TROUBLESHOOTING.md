# Guide de Dépannage

Ce guide vous aide à résoudre les problèmes courants du template Sports Streaming.

## 🔍 Diagnostic des Erreurs

### Console du Navigateur
Ouvrez les outils de développement (F12) et consultez l'onglet **Console** pour voir les messages :

#### Messages Normaux (OK) ✅
```
⚠️ Clés API non configurées - Utilisation des données de démonstration
📖 Consultez API_SETUP.md pour configurer les APIs
ℹ️ Rafraîchissement automatique désactivé - APIs non configurées
✅ ServiceWorker enregistré avec succès
```

#### Messages d'Erreur à Corriger ❌
```
Failed to load resource: net::ERR_NAME_NOT_RESOLVED
Refused to execute script from '<URL>' because its MIME type ('image/png') is not executable
ServiceWorker registration failed
```

## 🚨 Problèmes Courants et Solutions

### 1. Erreurs d'Images (ERR_NAME_NOT_RESOLVED)

**Symptôme :** Erreurs dans la console avec des URLs comme `000000:1` ou `FFFFFF:1`

**Cause :** URLs d'images placeholder malformées

**Solution :**
- ✅ **RÉSOLU dans la version v2.1** - Toutes les images ont été remplacées par des divs stylées
- Plus aucune dépendance aux services externes d'images
- Les logos d'équipes et chaînes utilisent maintenant des gradients CSS

**Avant (problématique) :**
```html
<img src="https://via.placeholder.com/30x30/FFFFFF/000000?text=RM"/>
```

**Après (corrigé) :**
```html
<div class='team-logo' style='width: 30px; height: 30px; background: #FFFFFF; color: #000000; display: flex; align-items: center; justify-content: center; border-radius: 50%; font-size: 10px; font-weight: bold;'>RM</div>
```

### 2. Erreurs API (403, 429)

**Symptômes :**
- `Failed to load resource: the server responded with a status of 403`
- `Failed to load resource: the server responded with a status of 429`

**Causes et Solutions :**

#### Erreur 403 - Accès Interdit
- **Cause :** Clé API invalide ou expirée
- **Solution :** Vérifiez votre clé API dans `API_SETUP.md`

#### Erreur 429 - Limite Atteinte
- **Cause :** Trop de requêtes API
- **Solution :** 
  - Attendez la réinitialisation du quota
  - Réduisez la fréquence de rafraîchissement
  - Passez à un plan payant

### 3. Service Worker Non Trouvé

**Symptôme :** `ServiceWorker registration failed`

**Solutions :**
1. **Uploadez le fichier sw.js** dans le répertoire racine de votre site
2. **Vérifiez le chemin** - Le template essaie plusieurs chemins automatiquement
3. **Ignorez l'erreur** - Le site fonctionne sans Service Worker

### 4. Erreurs YouTube/DoubleClick

**Symptômes :**
- `www.youtube.com/youtubei/v1/log_event: 499`
- `static.doubleclick.net/instream/ad_status.js: 499`

**Cause :** Bloqueurs de publicité ou restrictions réseau

**Solutions :**
- ✅ **Normal** - Ces erreurs n'affectent pas le fonctionnement
- Désactivez temporairement le bloqueur de pub pour tester
- Remplacez les vidéos YouTube par d'autres sources si nécessaire

### 5. Erreurs MIME Type

**Symptôme :** `Refused to execute script because its MIME type ('image/png') is not executable`

**Cause :** Images traitées comme du JavaScript

**Solution :** ✅ **Corrigé** - Les placeholders utilisent maintenant des SVG inline

## 🔧 Mode Débogage

### Activer les Logs Détaillés
Ajoutez cette ligne dans la console pour plus de détails :
```javascript
localStorage.setItem('debug', 'true');
```

### Désactiver les APIs Temporairement
```javascript
// Dans la console
API_CONFIG.football.rapidapi.headers['X-RapidAPI-Key'] = 'DISABLED';
```

### Forcer le Rechargement des Données
```javascript
// Dans la console
initializeAPIData();
```

## 📊 Vérification du Fonctionnement

### Checklist de Base ✅
- [ ] Le template s'affiche correctement
- [ ] La navigation par onglets fonctionne
- [ ] Le mode sombre/clair fonctionne
- [ ] Les données de démonstration s'affichent
- [ ] Pas d'erreurs critiques dans la console

### Checklist Avancée (avec APIs) ✅
- [ ] Les clés API sont configurées
- [ ] Les matchs en direct se mettent à jour
- [ ] Les classements s'affichent
- [ ] Les actualités se chargent
- [ ] Le rafraîchissement automatique fonctionne

## 🛠️ Solutions par Plateforme

### Blogger/Blogspot
1. **Vérifiez la syntaxe XML** - Aucune erreur dans l'éditeur
2. **Testez en mode aperçu** avant de publier
3. **Sauvegardez** toujours avant les modifications

### Hébergement Personnel
1. **HTTPS requis** pour les APIs
2. **CORS configuré** pour les requêtes externes
3. **Service Worker** uploadé dans le bon répertoire

## 🔄 Réinitialisation

### Réinitialiser les Paramètres
```javascript
// Dans la console
localStorage.clear();
location.reload();
```

### Restaurer les Données de Démonstration
```javascript
// Dans la console
loadDemoData();
```

### Réinstaller le Template
1. Sauvegardez vos personnalisations
2. Téléchargez la dernière version
3. Réappliquez vos modifications

## 📞 Obtenir de l'Aide

### Informations à Fournir
Quand vous demandez de l'aide, incluez :
- **Navigateur et version** (Chrome 120, Firefox 115, etc.)
- **Plateforme** (Blogger, hébergement personnel)
- **Messages d'erreur complets** de la console
- **Étapes pour reproduire** le problème

### Ressources Utiles
- [Documentation Blogger](https://support.google.com/blogger/)
- [API-Football Documentation](https://www.api-football.com/documentation-v3)
- [MDN Web Docs](https://developer.mozilla.org/)

## 🎯 Optimisation des Performances

### Réduire les Erreurs
1. **Configurez les APIs** pour éviter les erreurs 403/429
2. **Utilisez de vraies images** au lieu des placeholders
3. **Optimisez les intervalles** de rafraîchissement

### Améliorer la Vitesse
1. **Activez la compression** sur votre serveur
2. **Utilisez un CDN** pour les ressources statiques
3. **Minimisez les requêtes** API inutiles

---

**Note :** La plupart des erreurs mentionnées sont normales en mode démonstration et n'affectent pas le fonctionnement du site.
