# Guide d'Installation Blogger

Ce guide vous explique comment installer et configurer le template sur Blogger/Blogspot.

## 📋 Installation du Template

### Étape 1: Sauvegarde
1. Connectez-vous à votre compte Blogger
2. Allez dans **Thème** > **Sauvegarder/Restaurer**
3. C<PERSON>z sur **Télécharger le thème** pour sauvegarder votre thème actuel

### Étape 2: Installation
1. Dans **Thème**, cliquez sur **Modifier le code HTML**
2. Sélectionnez tout le code existant (Ctrl+A)
3. Copiez le contenu de `sports_streaming_template.xml`
4. Collez-le dans l'éditeur
5. Cliquez sur **Enregistrer**

## 🖼️ Configuration du Logo

### Méthode 1: Via l'interface Blogger
1. Allez dans **Mise en page**
2. Trouvez la section "Logo du Site" dans l'en-tête
3. Cliquez sur **Modifier**
4. Téléchargez votre logo depuis votre ordinateur
5. Aju<PERSON>z la taille si nécessaire
6. C<PERSON>z sur **Enregistrer**

### Méthode 2: Upload direct
1. Allez dans **Paramètres** > **Autres**
2. Dans "Favicon", téléchargez votre logo
3. Le logo apparaîtra automatiquement dans l'en-tête

### Spécifications recommandées
- **Format**: PNG ou JPG
- **Taille**: 120x40 pixels (ratio 3:1)
- **Poids**: Maximum 100 KB
- **Fond**: Transparent (PNG) recommandé

## 📢 Configuration des Publicités

### Emplacements disponibles
Le template inclut 6 emplacements publicitaires :

1. **Haut de page** - Sous le header
2. **Milieu de page** - Entre les sections
3. **Bas de page** - Avant le footer
4. **Sidebar** - Barre latérale (3 emplacements)

### Ajouter des publicités

#### Via Google AdSense:
1. Allez dans **Revenus** dans Blogger
2. Configurez AdSense
3. Les publicités s'afficheront automatiquement

#### Via widgets HTML:
1. Allez dans **Mise en page**
2. Trouvez les sections publicitaires
3. Cliquez sur **Ajouter un gadget**
4. Sélectionnez **HTML/JavaScript**
5. Collez votre code publicitaire
6. Donnez un titre (ex: "Publicité")
7. Cliquez sur **Enregistrer**

### Codes publicitaires supportés
- Google AdSense
- Amazon Associates
- Bannières personnalisées
- Code HTML/JavaScript

### Exemple de code publicitaire
```html
<!-- Bannière 728x90 -->
<div style="text-align: center;">
  <img src="votre-banniere.jpg" alt="Publicité" style="max-width: 100%; height: auto;">
</div>

<!-- Google AdSense -->
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-XXXXXXXXXX"
     data-ad-slot="XXXXXXXXXX"
     data-ad-format="auto"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>
```

## 🎛️ Configuration des Widgets

### Widgets inclus par défaut
- **Logo du site** (Image)
- **Articles populaires**
- **Archives du blog**
- **Libellés/Catégories**
- **Publicités** (HTML)

### Ajouter des widgets supplémentaires
1. Allez dans **Mise en page**
2. Cliquez sur **Ajouter un gadget** dans la sidebar
3. Choisissez parmi :
   - **Profil** - Informations sur l'auteur
   - **Liste de liens** - Liens utiles
   - **Texte** - Contenu personnalisé
   - **Flux** - RSS d'autres sites
   - **Sondage** - Enquêtes pour les visiteurs

### Widgets recommandés pour un site sportif
```
📊 Sondage: "Qui va gagner le championnat ?"
🔗 Liste de liens: Sites sportifs partenaires
📰 Flux RSS: Actualités sportives externes
👤 Profil: Présentation de l'équipe éditoriale
📧 Newsletter: Inscription à la newsletter
```

## 🎨 Personnalisation

### Couleurs du thème
Modifiez les variables CSS dans le template :
```css
:root {
  --dark-primary: #000080;    /* Bleu foncé */
  --light-primary: #0000FF;   /* Bleu clair */
  --accent-color: #FF0000;    /* Rouge accent */
  --background-dark: #121212; /* Fond sombre */
  --background-light: #f5f5f5; /* Fond clair */
}
```

### Polices personnalisées
Remplacez la police dans le CSS :
```css
--font-family: 'Votre-Police', sans-serif;
```

N'oubliez pas d'ajouter le lien Google Fonts dans le `<head>`.

## 📱 Test et Optimisation

### Test sur différents appareils
1. Utilisez l'aperçu Blogger
2. Testez sur mobile, tablette, desktop
3. Vérifiez la vitesse de chargement

### Optimisation SEO
1. Allez dans **Paramètres** > **Préférences de recherche**
2. Activez la description de recherche personnalisée
3. Configurez les balises meta
4. Ajoutez un sitemap

### Analytics
1. Créez un compte Google Analytics
2. Ajoutez le code de suivi dans **Thème** > **Modifier le code HTML**
3. Collez le code avant `</head>`

## 🔧 Maintenance

### Mises à jour du template
1. Sauvegardez toujours avant une mise à jour
2. Testez sur un blog de test d'abord
3. Vérifiez que vos widgets fonctionnent toujours

### Sauvegarde régulière
- Exportez votre blog mensuellement
- Sauvegardez le code du template
- Gardez une copie de vos widgets personnalisés

## 🆘 Dépannage

### Problèmes courants

#### Le logo ne s'affiche pas
- Vérifiez que l'image est publique
- Utilisez un lien direct vers l'image
- Respectez les dimensions recommandées

#### Les publicités ne s'affichent pas
- Vérifiez le code publicitaire
- Assurez-vous que AdSense est approuvé
- Testez avec une bannière simple d'abord

#### Le template ne se charge pas
- Vérifiez la syntaxe XML
- Recherchez les erreurs dans la console
- Restaurez la sauvegarde si nécessaire

#### Problèmes de responsive
- Testez sur différents appareils
- Vérifiez les media queries CSS
- Ajustez les tailles si nécessaire

### Support technique
- Consultez l'aide Blogger officielle
- Vérifiez les forums de la communauté
- Contactez le support si nécessaire

## 📊 Statistiques et Suivi

### Métriques importantes
- Temps de chargement des pages
- Taux de rebond
- Pages vues par session
- Revenus publicitaires

### Outils recommandés
- Google Analytics
- Google Search Console
- PageSpeed Insights
- GTmetrix

---

**Conseil**: Commencez par une configuration simple, puis ajoutez progressivement des fonctionnalités avancées.
