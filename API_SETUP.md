# Configuration des APIs Sportives

Ce guide vous explique comment configurer les APIs pour récupérer des données sportives en temps réel.

## 🔑 APIs Recommandées

### 1. API-Football (RapidAPI) - **RECOMMANDÉ**
- **URL**: https://rapidapi.com/api-sports/api/api-football
- **Avantages**: Données complètes, mises à jour en temps réel, support multiple sports
- **Prix**: Gratuit jusqu'à 100 requêtes/jour, plans payants disponibles
- **Données**: Matchs en direct, classements, statistiques, joueurs

### 2. Football-Data.org
- **URL**: https://www.football-data.org/
- **Avantages**: API gratuite, données européennes complètes
- **Prix**: Gratuit jusqu'à 10 requêtes/minute
- **Données**: Ligues européennes, matchs, classements

### 3. NewsAPI (pour les actualités)
- **URL**: https://newsapi.org/
- **Avantages**: Actualités sportives en temps réel
- **Prix**: Gratuit jusqu'à 1000 requêtes/jour
- **Données**: Articles de presse sportive

## 🛠️ Configuration

### Étape 1: Obtenir les clés API

#### API-Football (RapidAPI):
1. Créez un compte sur [RapidAPI](https://rapidapi.com/)
2. Abonnez-vous à [API-Football](https://rapidapi.com/api-sports/api/api-football)
3. Copiez votre clé API depuis le dashboard

#### Football-Data.org:
1. Créez un compte sur [Football-Data.org](https://www.football-data.org/)
2. Générez un token API gratuit
3. Notez votre token

#### NewsAPI:
1. Créez un compte sur [NewsAPI](https://newsapi.org/)
2. Générez une clé API gratuite
3. Copiez votre clé

### Étape 2: Configuration dans le template

Ouvrez le fichier `sports_streaming_template.xml` et remplacez les placeholders :

```javascript
const API_CONFIG = {
  football: {
    rapidapi: {
      baseUrl: 'https://api-football-v1.p.rapidapi.com/v3',
      headers: {
        'X-RapidAPI-Key': 'VOTRE_CLE_RAPIDAPI_ICI', // ← Remplacez ici
        'X-RapidAPI-Host': 'api-football-v1.p.rapidapi.com'
      }
    },
    footballData: {
      baseUrl: 'https://api.football-data.org/v4',
      headers: {
        'X-Auth-Token': 'VOTRE_TOKEN_FOOTBALL_DATA_ICI' // ← Remplacez ici
      }
    }
  }
};

// Dans la fonction loadSportsNews(), remplacez :
'https://newsapi.org/v2/everything?q=football+sport&language=fr&sortBy=publishedAt&apiKey=VOTRE_CLE_NEWSAPI_ICI'
```

## 📊 Données Disponibles

### Matchs en Direct
- Score en temps réel
- Temps de jeu
- Statut du match
- Logos des équipes

### Classements
- Position des équipes
- Points, victoires, défaites
- Buts pour/contre
- Différence de buts

### Meilleurs Buteurs
- Nom du joueur
- Équipe
- Nombre de buts
- Photo du joueur

### Actualités
- Titre de l'article
- Description
- Image
- Date de publication
- Lien vers l'article complet

## 🔧 Personnalisation

### Modifier les ligues suivies
Dans le fichier template, modifiez les IDs des ligues :

```javascript
// IDs des ligues (API-Football)
const leagues = [
  39,  // Premier League
  61,  // Ligue 1
  140, // La Liga
  78,  // Bundesliga
  135, // Serie A
  2    // Champions League
];
```

### Ajuster la fréquence de mise à jour
```javascript
// Matchs en direct: toutes les 30 secondes
setInterval(() => loadLiveMatches(), 30000);

// Classements: toutes les 5 minutes
setInterval(() => loadLeagueStandings(), 300000);

// Actualités: toutes les 10 minutes
setInterval(() => loadSportsNews(), 600000);
```

## 🚨 Gestion des Erreurs

### Problèmes CORS
Si vous rencontrez des erreurs CORS, utilisez un proxy :
```javascript
const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
fetch(proxyUrl + apiUrl, options);
```

### Limites de taux
- Respectez les limites de chaque API
- Implémentez un cache local
- Utilisez les données de démonstration en fallback

### Données de secours
Le template inclut des données de démonstration qui s'affichent si les APIs ne répondent pas.

## 📱 Test et Débogage

### Console du navigateur
Ouvrez les outils de développement (F12) pour voir :
- Les requêtes API
- Les erreurs éventuelles
- Les données reçues

### Logs utiles
```javascript
console.log('Données API reçues:', data);
console.error('Erreur API:', error);
```

## 🔒 Sécurité

### Protection des clés API
- Ne jamais exposer les clés dans le code client
- Utilisez un backend pour les requêtes sensibles
- Implémentez une rotation des clés

### HTTPS obligatoire
- Toutes les APIs nécessitent HTTPS
- Assurez-vous que votre site utilise HTTPS

## 📈 Optimisation

### Cache des données
```javascript
// Cache simple en localStorage
const cacheKey = 'sports_data_' + Date.now();
localStorage.setItem(cacheKey, JSON.stringify(data));
```

### Compression des requêtes
```javascript
headers: {
  'Accept-Encoding': 'gzip, deflate, br'
}
```

## 🆘 Support

### Ressources utiles
- [Documentation API-Football](https://www.api-football.com/documentation-v3)
- [Documentation Football-Data](https://www.football-data.org/documentation/quickstart)
- [Documentation NewsAPI](https://newsapi.org/docs)

### Contact
Pour toute question technique, consultez la documentation officielle de chaque API ou contactez leur support.

---

**Note**: Les APIs gratuites ont des limitations. Pour un usage professionnel, considérez les plans payants.
